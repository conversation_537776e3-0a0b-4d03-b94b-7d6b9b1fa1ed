type Role = {
    id: string;
    name: string;
};
export interface RolesStore {
    roles: Role[];
    permissionRoles: Role[];
    rolesMap: Record<string, any> | null;
    getRoles: (force?: Boolean) => Promise<any[]>;
    getPermissionRoles: (force?: Boolean) => Promise<any[]>;
}
declare const useRoles: import('zustand').UseBoundStore<import('zustand').StoreApi<RolesStore>>;
export default useRoles;
//# sourceMappingURL=useRole.d.ts.map
