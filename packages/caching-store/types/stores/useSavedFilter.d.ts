import { type AxiosResponse } from '@glidesystems/api';
type SavedFilterStore = {
    filtersByType: Record<string, Array<Record<string, any>>>;
    getFiltersByType: (type: string) => void;
    createNewFilter: (payload: Record<string, any>) => Promise<void>;
    deleteFilter: (type: string, id: string) => Promise<void>;
    getFilterById: (id: string) => Promise<AxiosResponse<any, any>>;
};
declare const useSavedFilter: import('zustand').UseBoundStore<import('zustand').StoreApi<SavedFilterStore>>;
export default useSavedFilter;
//# sourceMappingURL=useSavedFilter.d.ts.map
