export interface BookmarkPayload {
    name: string;
    folderPath: string;
    entityId: string;
    relationId?: string;
}
type BookmarkStore = {
    loaded: boolean;
    bookmarks: Array<Record<string, any>>;
    getBookmarks: (userId: string, force?: boolean) => Promise<Array<Record<string, any>>>;
    doesBookmarkExistForEntity: (userId: string, entityId: string) => Promise<boolean>;
    bookmarkEntity: (userId: string, payload: BookmarkPayload, isEdit?: boolean) => Promise<void>;
    unbookmarkEntity: (userId: string, entityId: string) => Promise<void>;
    batchBookmarkUpdate: (userId: string, payload: BookmarkPayload[]) => Promise<void>;
};
declare const useBookmarks: import('zustand').UseBoundStore<import('zustand').StoreApi<BookmarkStore>>;
export default useBookmarks;
//# sourceMappingURL=useBookmark.d.ts.map
