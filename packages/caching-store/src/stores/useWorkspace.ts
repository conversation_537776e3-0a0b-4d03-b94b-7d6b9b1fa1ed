/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { workspaceUrls, Workspace, fetch, DEFAULT_CLIENT_SIDE_LIMIT, authenticationService } from '@glidesystems/api';
import { getMountedApps, triggerAppChange, unloadApplication } from 'single-spa';

export interface WorkspaceStore {
    isLoaded: boolean;
    isLoading: boolean;
    workspaces: Workspace[];
    selectedWorkspace: Workspace | null;
    fetchWorkspaces: () => Promise<void>;
    changeWorkspace: (workspace: Workspace) => Promise<void>;
    removeWorkspace: () => Promise<void>;
    getByName: (name: string) => Workspace | undefined;
}

const EXCLUDED_APPS = ['@glidesystems/sidebar', '@glidesystems/header'];

/**
 * Clear workspace-specific cached data from stores
 */
export async function clearCachingStores() {
    try {
        // Import stores dynamically to avoid circular dependencies
        const [useBookmark, useSavedSearches, useEntityMetadata, useRole] = await Promise.all([
            import('./useBookmark').then((m) => m.default),
            import('./useSavedSearches').then((m) => m.default),
            import('./useEntityMetadata').then((m) => m.default),
            import('./useRole').then((m) => m.default),
        ]);

        // Reset stores to initial state
        const storesToReset = [
            { store: useBookmark, name: 'useBookmark', initialState: { loaded: false, bookmarks: [] } },
            {
                store: useSavedSearches,
                name: 'useSavedSearches',
                initialState: {
                    mySavedSearches: null,
                    editType: null,
                    selectedSearchViewers: [],
                    selectedSearchEditors: [],
                    selectedSavedSearch: null,
                    sharedSavedSearches: null,
                    isLoadingMySavedSearches: true,
                    isLoadingSharedSavedSearches: true,
                    isLoadingSelectedSearch: false,
                    editor: { open: false, searchCriteria: '', onSubmit: () => {} },
                },
            },
            {
                store: useEntityMetadata,
                name: 'useEntityMetadata',
                initialState: { entities: {}, entityAccess: {}, expiredAt: 0, entitiesByProperty: {} },
            },
            { store: useRole, name: 'useRole', initialState: { roles: null, rolesMap: null } },
        ];

        // Reset each store
        storesToReset.forEach(({ store, name, initialState }) => {
            try {
                store.setState(initialState);
                console.log(`Reset ${name} store`);
            } catch (error) {
                console.warn(`Failed to reset ${name} store:`, error);
            }
        });

        console.log('Successfully cleared caching stores for workspace change');
    } catch (error) {
        console.error('Failed to clear caching stores:', error);
    }
}

export async function remountAllMountedApps() {
    const mounted = getMountedApps().filter((name) => !EXCLUDED_APPS.includes(name));
    console.log('Handling workspace change — reloading applications:', mounted);

    // Clear caching stores before remounting apps
    await clearCachingStores();
    await Promise.all(mounted.map((name) => unloadApplication(name), { waitForUnmount: true }));
    await triggerAppChange();
}

const useWorkspace = create<WorkspaceStore>((set, get) => ({
    isLoaded: false,
    isLoading: false,
    workspaces: [],
    selectedWorkspace: null,
    fetchWorkspaces: async (force = false) => {
        try {
            if (get().isLoaded && !force) return;
            set({ isLoading: true });
            const response = await fetch({
                ...workspaceUrls.getWorkspaces,
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });

            let workspaces: Workspace[] = response.data.data || [];
            workspaces = workspaces.sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: 'base' }));
            set({ workspaces, isLoaded: true });
        } catch (error) {
            console.error('Failed to fetch workspaces:', error);
            set({ isLoaded: true });
        } finally {
            set({ isLoading: false });
        }
    },
    changeWorkspace: async (workspace: Workspace) => {
        set({ selectedWorkspace: workspace });
        const currWorkspaceName = authenticationService.getWorkspace()?.name;
        if (currWorkspaceName !== workspace.name) {
            window.localStorage.setItem('workspace', JSON.stringify(workspace));
            // Reload micro-frontends to apply the new workspace
            await remountAllMountedApps();
        }
    },
    removeWorkspace: async () => {
        set({ selectedWorkspace: null });
        const currWorkspaceName = authenticationService.getWorkspace()?.name;
        if (currWorkspaceName) {
            window.localStorage.removeItem('workspace');
            // Reload micro-frontends to apply the new workspace
            await remountAllMountedApps();
        }
    },
    getByName: (name: string) => {
        return get().workspaces.find((p) => p.name === name);
    },
}));

export default useWorkspace;
