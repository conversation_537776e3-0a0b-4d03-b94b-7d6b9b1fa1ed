/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useState } from 'react';
import { Button, Menu, MenuItem, Box, Typography, styled } from '@mui/material';
import { ChevronDown } from '@glidesystems/styleguide';
import { useWorkspace } from '@glidesystems/caching-store';
import { authenticationService, Workspace } from '@glidesystems/api';

const WorkspaceButton = styled(Button)(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
        '& .text': {
            display: 'none',
        },
        '& .MuiButton-endIcon': {
            marginLeft: '-2px',
        },
        borderRadius: '4px',
        width: '32px',
        minWidth: '32px',
    },
}));

const WorkspaceSelector: React.FC = () => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const { isLoading, workspaces, selectedWorkspace, fetchWorkspaces, changeWorkspace, getByName } = useWorkspace();
    // Get workspace name from local storage
    const storedWorkspace = authenticationService.getWorkspace();

    useEffect(() => {
        fetchWorkspaces();
    }, []);

    useEffect(() => {
        if (workspaces.length > 0) {
            const workspaceName = authenticationService.getWorkspace()?.name;
            const workspace = workspaceName ? getByName(workspaceName) : undefined;
            if (workspace) {
                changeWorkspace(workspace);
            } else {
                changeWorkspace(workspaces[0]);
            }
        }
    }, [workspaces]);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleWorkspaceSelect = async (workspace: Workspace) => {
        changeWorkspace(workspace);
        handleClose();
    };

    return (
        <Box>
            <WorkspaceButton
                sx={{
                    color: open ? 'black' : '#649BD6',
                    borderRadius: '4px',
                }}
                size="small"
                variant={open ? 'contained-white' : 'text'}
                onClick={handleClick}
                endIcon={
                    <ChevronDown
                        sx={{
                            width: 16,
                            height: 16,
                            transform: open ? 'rotate(180deg)' : 'none',
                            transition: 'transform 0.2s ease',
                        }}
                    />
                }
            >
                <span className="text">
                    {selectedWorkspace
                        ? selectedWorkspace.displayName
                        : storedWorkspace
                        ? storedWorkspace.displayName
                        : 'Select Workspace'}
                </span>
            </WorkspaceButton>

            <Menu
                id="workspace-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                sx={{
                    '& .MuiPaper-root': {
                        borderRadius: '4px',
                        marginTop: '4px',
                        padding: '4px 8px',
                        minWidth: anchorEl ? (anchorEl as HTMLElement).offsetWidth : undefined,
                    },
                }}
            >
                {isLoading && <MenuItem>Loading...</MenuItem>}
                {workspaces.map((workspace) => (
                    <MenuItem
                        key={workspace.id}
                        selected={selectedWorkspace?.id === workspace.id}
                        onClick={() => handleWorkspaceSelect(workspace)}
                        sx={{
                            borderRadius: '4px',
                            '&.Mui-selected': {
                                backgroundColor: '#334466 !important',
                                color: 'white !important',
                            },
                            '&.Mui-selected:hover': {
                                backgroundColor: '#3b4d75 !important',
                            },
                            '&:hover': {
                                backgroundColor: '#334466 !important',
                                color: 'white',
                            },
                        }}
                    >
                        <Box>
                            <Typography variant="inherit" sx={{ fontWeight: 500 }}>
                                {workspace.displayName}
                            </Typography>
                        </Box>
                    </MenuItem>
                ))}
            </Menu>
        </Box>
    );
};

export default WorkspaceSelector;
