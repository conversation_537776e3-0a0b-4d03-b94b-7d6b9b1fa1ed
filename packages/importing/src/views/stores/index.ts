/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, ruleUrls, DEFAULT_CLIENT_SIDE_LIMIT } from '@glidesystems/api';
import { IMPORT_FIELD_TYPE, IMPORT_STEP } from '../steps/Diagram/utils';
import { TemplateType } from '../utils';

export const IMPORT_TYPES_OPTIONS = {
    'Single Entity Type': {
        label: 'Single Entity Type',
        value: 'Single Entity Type',
        group: 'Entity Type',
    },
    'Multiple Entity Types': {
        label: 'Multiple Entity Types',
        value: 'Multiple Entity Types',
        group: 'Entity Type',
    },
    Relation: {
        label: 'Relation',
        value: 'Relation',
        group: 'Relation',
    },
    'Decomposition With Parent And Child': {
        label: 'Decomposition With Parent And Child',
        value: 'Decomposition With Parent And Child',
        group: 'Decomposition',
    },
    'Decomposition With Level': {
        label: 'Decomposition With Level',
        value: 'Decomposition With Level',
        group: 'Decomposition',
    },
} as const;
export const ADMIN_IMPORT_TYPE_OPTIONS = {
    ...IMPORT_TYPES_OPTIONS,
    User: {
        label: 'User',
        value: 'User',
        group: 'Administration',
    },
    UserGroup: {
        label: 'User Group',
        value: 'UserGroup',
        group: 'Administration',
    },
} as const;
export type ImportType = keyof typeof ADMIN_IMPORT_TYPE_OPTIONS;

export interface DataMapping {
    columnName: string;
    target: string;
    type: IMPORT_FIELD_TYPE;
}

export const DELIMITER = {
    ',': {
        label: 'Comma (,)',
        value: ',',
    },
    '|': {
        label: 'Vertical Bar (|)',
        value: '|',
    },
    ';': {
        label: 'Semicolon (;)',
        value: ';',
    },
    '\t': {
        label: 'Tab (\\t)',
        value: '\t',
    },
};

export interface OptionType {
    label: string;
    value: string;
}

export type DelimiterType = keyof typeof DELIMITER;

export const LENGTH_VALIDATION = {
    TRUNCATE: { label: 'Truncate', value: 'TRUNCATE' },
    REJECT: { label: 'Reject', value: 'REJECT' },
};

export type LengthValidationType = keyof typeof LENGTH_VALIDATION;

export interface ParsingMetadata {
    templateName: string;
    templateDescription: string;
    saveAsTemplate: boolean;
    multiListDelimiter: DelimiterType;
    cascadeDelimiter: DelimiterType;
    csvDelimiter: DelimiterType;
    lengthValidation: LengthValidationType;
    updateExistingTemplate?: boolean;
    importTemplateUpdateType?: 'new' | 'update';
    warningRuleDecisions?: WarningRuleDecision[];
}

export interface WarningRuleDecision {
    ruleId: string;
    action: RuleDecision;
}

export type RuleDecision = 'ACCEPT' | 'REJECT';

export interface BusinessRule {
    id: string;
    name: string;
    description: string;
    entityType: string;
    eventType: string;
    tenantId: string;
    createdBy: string;
    createdDate: Date;
    expression: string;
    when: null;
    then: Then;
    ruleType: string;
    actions: any[];
}

export interface Then {
    status: string;
    message: string;
    modify: string;
}

export type UploadFileType = 'local' | 'url' | 'others';
export enum OtherSource {
    GoogleDrive = 'Google Drive',
    GoogleSheet = 'Google Sheets',
    S3Bucket = 'S3 Bucket',
    DataStorage = 'Data Storage',
    Snowflake = 'Snowflake',
    Redshift = 'Redshift',
    BigQuery = 'Big Query',
}

interface ImportStore {
    files: File[];
    attachments: File[];
    dataMapping: DataMapping[] | Record<string, DataMapping[]>;
    allowAttributesWithIdentifier: boolean; // If ChangeOrder is selected allow
    dataTypeColumn: string;
    typesForMultipleDataTypeSelection: string[];
    importType: ImportType;
    activeStep: number;
    headerColumnsFromFile: string[];
    selectedType: string;
    uploadFileType: UploadFileType;
    selectedDataSource: OtherSource;
    businessRules: BusinessRule[];
    loadingBusinessRules: boolean;
    setFiles: (files: File[]) => void;
    setAttachments: (files: File[]) => void;
    setHeaderColumnsFromFile: (columns: string[]) => void;
    setSelectedType: (selectedType: string) => void;
    incrementActiveStep: () => void;
    decrementActiveStep: () => void;
    setDataMapping: (dataMapping: DataMapping[] | Record<string, DataMapping[]>) => void;
    parsingMetadata: ParsingMetadata;
    setParsingMetadata: (parsingMetadata: ParsingMetadata) => void;
    isExistingTemplate: boolean;
    setIsExistingTemplate: (existingTemplate: boolean) => void;
    existingTemplateInformation: { name: string; description: string; id: string };
    setExistingTemplateInformation: (name: string, description: string, id: string) => void;
    currentRequiredCount: number;
    setCurrentRequiredCount: (requiredCount: number) => void;
    reset: () => void;
    resetExistingTemplate: () => void;
    resetDataType: () => void;
    selectedTemplateType: TemplateType;
    setSelectedTemplateType: (templateType: TemplateType) => void;
    setSelectedImportType: (importType: ImportType) => void;
    setDataTypeColumn: (dataTypeColumn: string) => void;
    setTypesForMultipleColumnSelection: (types: string[]) => void;
    setUploadFileType: (uploadFileType: UploadFileType) => void;
    setSelectedDataSource: (selectedDataSource: OtherSource) => void;
    getAllBusinessRules: () => Promise<void>;
    setBusinessRules: (rules: BusinessRule[]) => void;
    setLoadingBusinessRules: (loading: boolean) => void;
    setAllowIdentifierAttributes: (allowIdentifier: boolean) => void;
}

export const STEPS = {
    [IMPORT_STEP.UPLOAD_FILE]: {
        id: 0,
        label: 'Select File',
    },
    [IMPORT_STEP.SELECT_DATA_TYPE]: {
        id: 1,
        label: 'Select Import Type & Data Type',
    },
    [IMPORT_STEP.IMPORT_REFERENCE]: {
        id: 2,
        label: 'Define Data Mapping',
    },
    [IMPORT_STEP.PARSING]: {
        id: 3,
        label: 'Import Preferences',
    },
};

const INITIAL_STATE = {
    files: [],
    attachments: [],
    selectedTemplateType: 'new' as TemplateType,
    dataTypeColumn: null,
    uploadFileType: 'local' as UploadFileType,
    selectedDataSource: null,
    typesForMultipleDataTypeSelection: null,
    allowAttributesWithIdentifier: false,
    importType: null,
    dataMapping: null,
    currentRequiredCount: 0,
    isExistingTemplate: false,
    businessRules: [],
    loadingBusinessRules: false,
    parsingMetadata: {
        templateName: null,
        templateDescription: null,
        saveAsTemplate: false,
        multiListDelimiter: null,
        csvDelimiter: null,
        cascadeDelimiter: null,
        lengthValidation: null,
    },
    activeStep: STEPS[IMPORT_STEP.UPLOAD_FILE].id,
    selectedType: null,
    headerColumnsFromFile: [],
    existingTemplateInformation: null,
};

export const useImportStore = create<ImportStore>((set, get) => ({
    ...INITIAL_STATE,
    setFiles: (files: File[]) => {
        set({ files });
    },
    setAttachments: (files: File[]) => {
        set({ attachments: files });
    },
    setHeaderColumnsFromFile: (columns: string[]) => {
        set({ headerColumnsFromFile: columns });
    },
    setSelectedType: (selectedType: string) => {
        set({ selectedType });
        get().getAllBusinessRules();
    },
    incrementActiveStep: () => {
        set({ activeStep: get().activeStep + 1 });
    },
    decrementActiveStep: () => {
        set({ activeStep: get().activeStep - 1 });
    },
    setDataMapping: (dataMapping: DataMapping[] | Record<string, DataMapping[]>) => {
        if (!Array.isArray(dataMapping)) {
            set({ dataMapping: { ...get().dataMapping, ...dataMapping } });
        } else {
            set({ dataMapping });
        }
    },
    setParsingMetadata: (parsingMetadata: ParsingMetadata) => {
        set({ parsingMetadata });
    },
    setIsExistingTemplate: (isExistingTemplate: boolean) => {
        set({ isExistingTemplate });
    },
    setExistingTemplateInformation: (name: string, description: string, id: string) => {
        set({
            existingTemplateInformation: {
                name,
                description,
                id,
            },
            isExistingTemplate: true,
        });
    },
    reset: () => {
        set(INITIAL_STATE);
    },
    setCurrentRequiredCount: (currentRequiredCount: number) => {
        set({ currentRequiredCount });
    },
    resetDataType: () => {
        set({
            isExistingTemplate: false,
            existingTemplateInformation: null,
            parsingMetadata: null,
            dataMapping: null,
            selectedType: null,
            businessRules: [],
            loadingBusinessRules: false,
        });
    },
    resetExistingTemplate: () => {
        set({
            isExistingTemplate: false,
            existingTemplateInformation: null,
            parsingMetadata: null,
            dataMapping: null,
        });
    },
    setSelectedTemplateType: (importType) => {
        set({ selectedTemplateType: importType });
    },
    setSelectedImportType: (importType) => {
        set({ importType: importType });
    },
    setDataTypeColumn: (dataTypeColumn) => {
        set({ dataTypeColumn });
    },
    setTypesForMultipleColumnSelection: (types) => {
        set({ typesForMultipleDataTypeSelection: types });
        get().getAllBusinessRules();
    },
    setUploadFileType: (uploadFileType) => {
        set({ uploadFileType });
    },
    setSelectedDataSource: (selectedDataSource) => {
        set({ selectedDataSource });
    },
    setBusinessRules: (businessRules: BusinessRule[]) => {
        set({ businessRules });
    },
    setLoadingBusinessRules: (loadingBusinessRules: boolean) => {
        set({ loadingBusinessRules });
    },
    getAllBusinessRules: async () => {
        try {
            const { selectedType, typesForMultipleDataTypeSelection } = get();
            set({ loadingBusinessRules: true });
            const isMultipleTypes = typesForMultipleDataTypeSelection != null;

            const { data } = await fetch({
                ...ruleUrls.getRules,
                qs: {
                    entityType: isMultipleTypes ? null : selectedType,
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });

            if (data) {
                let warningRules = data.data.filter((rule: BusinessRule) => rule.then.status === 'Warning');
                if (isMultipleTypes) {
                    warningRules = warningRules
                        .filter((rule: BusinessRule) => typesForMultipleDataTypeSelection.includes(rule.entityType))
                        .sort((a: BusinessRule, b: BusinessRule) => {
                            const indexA = typesForMultipleDataTypeSelection.indexOf(a.entityType);
                            const indexB = typesForMultipleDataTypeSelection.indexOf(b.entityType);
                            return indexA - indexB;
                        });
                }
                set({ businessRules: warningRules });
            }
        } catch (error) {
            console.error('Error fetching business rules:', error);
            set({ businessRules: [] });
        } finally {
            set({ loadingBusinessRules: false });
        }
    },
    setAllowIdentifierAttributes: (allowAttributesWithIdentifier) => {
        set({ allowAttributesWithIdentifier });
    },
}));
