/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { FullHeightGrid, ContentWrapper, AnimatedContainer } from '../../components/Layout/Common';
import { selectAppReducer } from '../../selectors';
import DocumentView from '../../components/AttributeDetail/DocumentView';
import AttributeDetail from '../../components/AttributeDetail/AttributeDetail';
import ClassificationAttributes from '../../components/AttributeDetail/ClassificationAttributes';
import AttachmentFiles from '../../components/AttributeDetail/AttachmentFiles';
import get from 'lodash/get';
import { getDocumentFiles } from '../../actions';
import { SYSTEM_ENTITY_TYPE } from '@glidesystems/api';

const DocumentAttributesView = ({ isFileView }: { isFileView?: boolean } = { isFileView: false }) => {
    const {
        detailEntity,
        detailSchema,
        classificationSchema,
        sessions: { isLoadingEntityDetail },
    } = useSelector(selectAppReducer);

    const [documentFiles, setDocumentFiles] = useState([]);
    const [selectedFile, setSelectedFile] = useState(null);
    const [isFileLoading, setIsFileLoading] = useState(true);
    const [isFilesLoaded, setIsFilesLoaded] = useState(false);
    const [isLoaded, setIsLoaded] = useState(false);

    const fetchDocuments = useCallback(() => {
        if (detailEntity?.properties?.type === SYSTEM_ENTITY_TYPE.FILE) return;
        setIsFileLoading(true);
        getDocumentFiles(detailEntity.id).then((response) => {
            if (response.result) {
                const allFiles: Array<Record<string, any>> = Object.values(response.result.data.fileResponses);
                setDocumentFiles(allFiles);
                if (allFiles?.length === 1) {
                    setSelectedFile(allFiles[0]);
                } else {
                    const primaryFileId = get(detailEntity, ['properties', 'primaryFileId']);
                    allFiles.forEach((file) => {
                        if (file?.id === primaryFileId) {
                            setSelectedFile(file);
                        }
                    });
                }
            }
            setIsLoaded(true);
            setIsFileLoading(false);
        });
    }, [detailEntity.id]);

    useEffect(() => {
        if (detailEntity && !isLoaded) {
            fetchDocuments();
        }
    }, [detailEntity]);

    useEffect(() => {
        if (isFileView && detailEntity) {
            setDocumentFiles([detailEntity]);
            setSelectedFile(detailEntity);
            setIsFileLoading(false);
            setIsFilesLoaded(true);
        }
    }, [isFileView, detailEntity]);

    return (
        !isLoadingEntityDetail && (
            <>
                <FullHeightGrid item xs={12} md={8} lg={8}>
                    <ContentWrapper>
                        <div className="animatedContainer leftPanel" style={{ height: '100%' }}>
                            <DocumentView
                                files={documentFiles}
                                detailEntity={detailEntity}
                                selectedFile={selectedFile}
                                isFileLoading={isFileLoading}
                                isFileView={isFileView}
                            />
                        </div>
                    </ContentWrapper>
                </FullHeightGrid>

                <FullHeightGrid item xs={12} md={4} lg={4}>
                    <ContentWrapper>
                        <AnimatedContainer
                            animation={{
                                initial: { opacity: 0, scale: 0.98 },
                                animate: { opacity: 1, scale: 1 },
                            }}
                            className="rightPanel"
                        >
                            <AttributeDetail
                                title="Information"
                                isDocument
                                data={detailEntity}
                                detailSchema={detailSchema}
                            />
                            {!isFileView && (
                                <AttachmentFiles
                                    files={documentFiles}
                                    onSelectFile={setSelectedFile}
                                    isLoaded={isFileLoading}
                                    detailEntity={detailEntity}
                                />
                            )}
                            <ClassificationAttributes
                                detailEntity={detailEntity}
                                classificationSchema={classificationSchema}
                                detailSchema={detailSchema}
                            />
                        </AnimatedContainer>
                    </ContentWrapper>
                </FullHeightGrid>
            </>
        )
    );
};

export default DocumentAttributesView;
