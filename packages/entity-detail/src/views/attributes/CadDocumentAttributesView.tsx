/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { AnimatedContainer } from '../../components/Layout/Common';
import { selectAppReducer } from '../../selectors';
import DocumentView from '../../components/AttributeDetail/DocumentView';
import AttributeDetail from '../../components/AttributeDetail/AttributeDetail';
import ClassificationAttributes from '../../components/AttributeDetail/ClassificationAttributes';
import AttachmentFiles from '../../components/AttributeDetail/AttachmentFiles';
import { IconButton, Slide, styled, Typography } from '@mui/material';
import Paper from '@mui/material/Paper';
import { getDocumentFiles } from '../../actions';
import get from 'lodash/get';
import { CloseIcon } from '@glidesystems/styleguide';
import { capitalize } from '../../utils/helper';
import { Discussions } from '../discussions/Discussions';
import ChangeManagement from '../ChangeManagement';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { CUSTOM_EVENTS } from '../../constants/events';

export const Wrapper = styled(Paper)(({ theme }) => ({
    borderRadius: 0,
    boxShadow: 'none',
    height: '100%',
    width: '100%',
    WebkitOverflowScrolling: 'touch',
    WebkitOverflow: 'auto',
    display: 'flex',
    gap: '10px',
    flexDirection: 'row',
    '& .ruleHeader': {
        position: 'sticky',
        top: 0,
        background: theme.palette.glide.background.normal.tertiary,
        padding: '8px 10px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        color: theme.palette.glide.text.normal.tertiary,
        '& .closeBtn': {
            alignSelf: 'flex-start',
            color: 'inherit',
        },
        zIndex: 1000,
    },
    flexGrow: 1,
}));

const CadDocumentAttributesView = ({ isFileView }: { isFileView?: boolean } = { isFileView: false }) => {
    const {
        detailEntity,
        detailSchema,
        classificationSchema,
        sessions: { isLoadingEntityDetail },
    } = useSelector(selectAppReducer);

    const [documentFiles, setDocumentFiles] = useState([]);
    const [selectedFile, setSelectedFile] = useState<Record<string, any> | null>(null);
    const [isFileLoading, setIsFileLoading] = useState(true);
    const [isLoaded, setIsLoaded] = useState(false);
    const [activeSidebar, setActiveSidebar] = useState<SidebarType>('details');
    const [selectedPartId, setSelectedPartId] = useState<string>();
    const [selectedPartName, setSelectedPartName] = useState<string>();

    const fetchDocuments = useCallback(() => {
        setIsFileLoading(true);
        getDocumentFiles(detailEntity.id).then((response) => {
            if (response.result) {
                const allFiles: Array<Record<string, any>> = Object.values(response.result.data.fileResponses);
                setDocumentFiles(allFiles);
                const primaryFileId = get(detailEntity, ['properties', 'primaryFileId']);
                allFiles.forEach((file) => {
                    if (file?.id === primaryFileId) {
                        setSelectedFile(file);
                    }
                });
            }
            setIsLoaded(true);
            setIsFileLoading(false);
        });
    }, [detailEntity.id]);

    useEffect(() => {
        if (detailEntity && !isLoaded) {
            fetchDocuments();
        }
    }, [detailEntity]);

    useEffect(() => {
        if (isFileView && detailEntity) {
            setDocumentFiles([detailEntity]);
            setSelectedFile(detailEntity);
            setIsFileLoading(false);
        }
    }, [isFileView, detailEntity]);

    useEffect(() => {
        const refreshTable = () => {
            fetchDocuments();
        };
        const toggleHighlightDiscussion = ({ detail }: CustomEvent<ToggleHighlightDiscussionCustomEvent>) => {
            setActiveSidebar('discussions');
            const messageElement = document.getElementById(`message-${detail.messageId}`);
            if (messageElement !== null) {
                if (detail.noteOpen === true) {
                    messageElement.style.backgroundColor = '#EBEEF7';
                    setTimeout(() => {
                        messageElement.scrollIntoView({ behavior: 'smooth' });
                    }, 100);
                } else {
                    messageElement.style.backgroundColor = 'transparent';
                }
            }
        };
        const openSidebar = ({ detail }: CustomEvent<OpenSideBarCustomEvent>) => {
            setActiveSidebar(detail.activeSidebar);
            if (
                detail.activeSidebar === 'issues' &&
                detail.selectedPartId !== undefined &&
                detail.selectedPartName !== undefined
            ) {
                setSelectedPartId(detail.selectedPartId);
                setSelectedPartName(detail.selectedPartName);
            }
            setTimeout(() => {
                window.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.RESIZE_CAD_VIEWER));
            }, 0);
        };

        window.addEventListener(CUSTOM_EVENTS.REFRESH_ATTACHMENTS_TABLE, refreshTable);
        window.addEventListener(CUSTOM_EVENTS.TOGGLE_HIGHLIGHT_DISCUSSION, toggleHighlightDiscussion);
        window.addEventListener(CUSTOM_EVENTS.OPEN_CAD_SIDEBAR, openSidebar);

        return () => {
            window.removeEventListener(CUSTOM_EVENTS.TOGGLE_HIGHLIGHT_DISCUSSION, toggleHighlightDiscussion);
            window.removeEventListener(CUSTOM_EVENTS.REFRESH_ATTACHMENTS_TABLE, refreshTable);
            window.removeEventListener(CUSTOM_EVENTS.OPEN_CAD_SIDEBAR, openSidebar);
        };
    }, []);

    return (
        !isLoadingEntityDetail && (
            <>
                <Wrapper>
                    <PanelGroup direction="horizontal" autoSaveId="schema-panels">
                        <Panel id="folder-hierarchy-panel" style={{ height: '100%' }}>
                            <div style={{ width: '100%', height: '100%' }}>
                                <DocumentView
                                    files={documentFiles}
                                    detailEntity={detailEntity}
                                    selectedFile={selectedFile}
                                    isFileLoading={isFileLoading}
                                    isFileView={isFileView}
                                    disableUpload={true}
                                />
                            </div>
                        </Panel>
                        {activeSidebar !== 'none' && (
                            <Panel
                                minSize={30}
                                defaultSize={45}
                                maxSize={50}
                                onResize={() => {
                                    window.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.RESIZE_CAD_VIEWER));
                                }}
                                style={{ height: '100%', position: 'relative' }}
                            >
                                <PanelResizeHandle
                                    style={{
                                        position: 'absolute',
                                        height: '100%',
                                        width: '8px',
                                        left: 0,
                                        top: 0,
                                    }}
                                ></PanelResizeHandle>
                                <Slide unmountOnExit in={activeSidebar !== undefined} direction="left">
                                    <Paper
                                        sx={{
                                            borderLeft: (theme) =>
                                                `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                            // width: '35%',
                                            overflow: activeSidebar === 'issues' ? 'visible' : 'auto',
                                            borderRadius: 0,
                                            boxShadow: 'none',
                                            '& .animatedContainer': {
                                                display: 'flex',
                                                flexDirection: 'column',
                                                minHeight: '100%',
                                            },
                                            height: '100%',
                                        }}
                                    >
                                        <AnimatedContainer
                                            animation={{
                                                initial: { opacity: 0, scale: 0.98 },
                                                animate: { opacity: 1, scale: 1 },
                                            }}
                                            className="rightPanel"
                                        >
                                            <div className="ruleHeader">
                                                <Typography
                                                    variant="title3"
                                                    sx={{ color: (theme) => theme.palette.glide.text.normal.tertiary }}
                                                >
                                                    {`${capitalize(activeSidebar)}${
                                                        activeSidebar === 'issues'
                                                            ? ' affecting ' + selectedPartName
                                                            : ''
                                                    }`}
                                                </Typography>
                                                <IconButton
                                                    className="closeBtn hiden-on-export"
                                                    onClick={() => {
                                                        setActiveSidebar('none');
                                                        setTimeout(() => {
                                                            window.dispatchEvent(
                                                                new CustomEvent(CUSTOM_EVENTS.RESIZE_CAD_VIEWER)
                                                            );
                                                        }, 0);
                                                    }}
                                                >
                                                    <CloseIcon />
                                                </IconButton>
                                            </div>
                                            {activeSidebar === 'details' && (
                                                <>
                                                    <AttributeDetail
                                                        title="Information"
                                                        isDocument
                                                        data={detailEntity}
                                                        detailSchema={detailSchema}
                                                    />
                                                    {!isFileView && (
                                                        <AttachmentFiles
                                                            files={documentFiles}
                                                            onSelectFile={setSelectedFile}
                                                            isLoaded={isFileLoading}
                                                            detailEntity={detailEntity}
                                                        />
                                                    )}
                                                    <ClassificationAttributes
                                                        detailEntity={detailEntity}
                                                        classificationSchema={classificationSchema}
                                                        detailSchema={detailSchema}
                                                    />
                                                </>
                                            )}
                                            {activeSidebar === 'discussions' && <Discussions />}
                                            {activeSidebar === 'issues' && selectedPartId !== undefined && (
                                                <ChangeManagement
                                                    selectedId={selectedPartId}
                                                    sx={{
                                                        height: 'calc(100vh - 245px)',
                                                        minHeight: 'calc(100vh - 245px)',
                                                    }}
                                                />
                                            )}
                                        </AnimatedContainer>
                                    </Paper>
                                </Slide>
                            </Panel>
                        )}
                    </PanelGroup>
                </Wrapper>
            </>
        )
    );
};

export default CadDocumentAttributesView;
