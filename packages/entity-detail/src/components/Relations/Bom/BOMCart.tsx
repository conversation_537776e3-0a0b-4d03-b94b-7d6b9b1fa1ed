/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Button, IconButton } from '@mui/material';
import { AgGridReact } from '@ag-grid-community/react';
import * as yup from 'yup';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../../selectors';
import {
    Loading,
    commonMessages,
    notifyError,
    buildValidationSchema,
    tableStyles,
    DeleteIcon,
    PropertyValueRenderer,
    isArrayType,
    notifySuccess,
} from '@glidesystems/styleguide';
import flatten from 'lodash/flatten';
import {
    entityUrls,
    batchRequest,
    batchRequestBody,
    HandleBatchRequest,
    Attribute,
    EntityDetail,
    RuleResult,
} from '@glidesystems/api';
import { Formik } from 'formik';
import { EditableRenderer } from '../../../utils/cellRenderers';
import { EXCLUDED_BOM_FIELDS, RELATION } from '../../../constants/common';
import isEmpty from 'lodash/isEmpty';
import { ColDef, GridOptions } from '@ag-grid-community/core';
import { useSchemaTree } from '@glidesystems/caching-store';

interface DecompositionResponse {
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    failureByIndex: Record<string, FailureEntry>;
}

interface FailureEntry {
    decomposition: {
        component: {
            idType: string;
            idValue: string;
        };
        contextualAlternates: {
            idType: string;
            idValue: string;
        }[];
        attributes: Record<string, any>;
    };
    errorMessage: string;
    errorCode: string;
    businessRuleResults: RuleResult[];
    index: number;
}

const DeleteRenderer = ({ value, clicked }) => {
    return (
        <IconButton color="error" onClick={() => clicked(value)}>
            <DeleteIcon />
        </IconButton>
    );
};

const buildInitialValue = (entities, attributes) => {
    return entities.reduce(
        (prevValues, currValues) => ({
            ...prevValues,
            [currValues.id]: attributes.reduce(
                (prev, curr) => ({
                    ...prev,
                    [curr.name]: curr.defaultValue,
                }),
                {}
            ),
        }),
        {}
    );
};

const buildEntitiesValidationSchema = (entities, attributes) => {
    return yup.object().shape(
        entities.reduce(
            (prev, cur) => ({
                ...prev,
                [cur.id]: buildValidationSchema(attributes),
            }),
            {}
        )
    );
};

export const handleBatchRequestRes = ({
    response,
    onSuccess = () => {},
    onFailed = () => {},
    successMessage = commonMessages.successRequest,
}: HandleBatchRequest<{ response: DecompositionResponse }>): void => {
    const { data } = response;
    const resDetails = flatten(data.map((r) => r.data));
    let successReq = [];
    let failedReq = [];

    resDetails.forEach((d) => {
        if (d.response.failureCount === 0) successReq.push(d);
        else failedReq.push(d);
    });

    const failedReqs = resDetails.filter((d) => d.response.failureCount > 0);
    if (failedReqs.length === 0) {
        notifySuccess(successMessage);
        onSuccess();
    } else {
        const ruleErrors: RuleResult[] = [];
        const errors: string[] = [];
        failedReqs.forEach((req) => {
            const failures = Object.values(req.response.failureByIndex || {});
            failures.forEach((failure) => {
                const rules = failure?.businessRuleResults;
                if (rules?.length > 0) {
                    ruleErrors.push(...rules);
                } else {
                    errors.push(failure.errorMessage);
                }
            });
        });
        let errorListHTML = '';
        if (ruleErrors.length > 0) {
            errorListHTML += `
		            <div>
		                <p><b>Some business rules were violated</b></p>
		                <ul style="margin: 4px; marginTop:0px;">
		                    ${ruleErrors.map((rule) => `<li>${rule.message}</li>`).join('')}
		                </ul>
		            </div>
		        `;
        }
        if (errors.length > 0) {
            errorListHTML += `
		            <div>
		                <p><b>Other Errors</b></p>
		                <ul style="margin: 4px;">
		                    ${errors.map((error) => `<li>${error}</li>`).join('')}
		                </ul>
		            </div>
		        `;
        }
        notifyError(errorListHTML, { autoClose: false });
        onFailed();
    }
};

const filterAndSortAttributes = (attributes: Record<string, Attribute>, attributeOrder: string[]) => {
    return Object.values(attributes)
        .filter(
            (attr) =>
                attr.visible && attr.mutable && !EXCLUDED_BOM_FIELDS.includes(attr.name) && isEmpty(attr.identifier)
        )
        .sort((a, b) => attributeOrder.indexOf(a.name) - attributeOrder.indexOf(b.name));
};

const DEFAULT_COLUMN_DEFINITIONS: ColDef[] = [
    {
        field: 'properties.name',
        headerName: 'Name',
        minWidth: 300,
        flex: 3,
    },
    {
        field: 'properties.type',
        headerName: 'Type',
        minWidth: 140,
        flex: 1,
        valueGetter: (props) => {
            if (props.context.schemaTreeMap) {
                return (
                    props.context.schemaTreeMap?.[props.data?.properties?.type]?.displayName ||
                    props.data?.properties?.type
                );
            }
            return props.data?.properties?.type;
        },
    },
    {
        field: 'properties.description',
        headerName: 'Description',
        minWidth: 140,
        valueFormatter: ({ value }) => {
            const plainText = value ? value.replace(/<\/?[^>]+>/gi, ' ') : value;
            return plainText;
        },
        cellRenderer: PropertyValueRenderer,
    },
];

const BOMCart = ({
    selectedRows,
    onClose,
    onCloseDrawer,
    refreshTable,
    parentGridRef,
    componentType,
    onSubmit = undefined,
    bomType,
    bomSchema,
}) => {
    const [data, setData] = useState([]);
    const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);
    const [initialValues, setInitialValues] = useState({});
    const formikRef = useRef(null);
    const [validationSchema, setValidationSchema] = useState({});
    const { selectedBOMs } = useSelector(selectAppReducer);
    const [loading, setLoading] = useState(false);
    const { schemaTreeMap } = useSchemaTree();

    useEffect(() => {
        if (bomSchema) {
            const {
                attributes,
                entityType: { attributeOrder },
            } = bomSchema;
            const filteredAttributes = filterAndSortAttributes(attributes, attributeOrder);
            setColumnDefs([
                ...DEFAULT_COLUMN_DEFINITIONS,
                ...filteredAttributes.map(
                    (attribute): ColDef => ({
                        field: attribute.name,
                        headerName: attribute.displayName,
                        minWidth: isArrayType(attribute.type) ? 400 : 150,
                        flex: 1,
                        cellRenderer: 'EditableRenderer',
                        cellRendererParams: {
                            attribute,
                        },

                        cellStyle: () => {
                            if (isArrayType(attribute.type)) {
                                return {
                                    padding: '0',
                                };
                            }
                        },
                        sortable: false,
                    })
                ),
                {
                    field: 'id',
                    headerName: '',
                    minWidth: 80,
                    width: 80,
                    cellRenderer: DeleteRenderer,
                    cellRendererParams: {
                        clicked: (id: string) => {
                            parentGridRef.current.api.getRowNode(id).setSelected(false);
                        },
                    },
                },
            ]);
        }
    }, [bomSchema]);

    useEffect(() => {
        const bomData = selectedRows.map((row) => ({
            ...row,
            componentQuantity: 1,
        }));
        setData(bomData);
    }, [selectedRows]);

    useEffect(() => {
        if (bomSchema) {
            const {
                attributes,
                entityType: { attributeOrder },
            } = bomSchema;
            const filteredAttributes = filterAndSortAttributes(attributes, attributeOrder);
            setInitialValues(buildInitialValue(data, filteredAttributes));
            setValidationSchema(buildEntitiesValidationSchema(data, filteredAttributes));
        }
    }, [data, bomSchema]);

    const gridRef = useRef<AgGridReact>();
    const gridOptions: GridOptions = useMemo(
        () => ({
            rowDragMultiRow: true,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                autoHeight: true,
                wrapText: true,
                cellStyle: {
                    display: 'block',
                },
            },
            headerHeight: 34,
            rowHeight: 36,
            columnDefs: columnDefs,
            rowSelection: 'multiple',
            rowModelType: 'clientSide',
            components: {
                EditableRenderer,
            },
            loadingOverlayComponent: Loading,
            onGridReady: () => gridRef.current.columnApi.autoSizeAllColumns(),
        }),
        [gridRef, columnDefs]
    );
    const handleSubmit = async (values, setSubmitting) => {
        if (selectedRows.length > 0) {
            try {
                setLoading(true);
                if (onSubmit) {
                    onSubmit(values, selectedRows, componentType, () => {
                        onClose();
                        onCloseDrawer();
                    });
                    return;
                }
                gridRef.current.api.showLoadingOverlay();

                const bodyData = selectedBOMs.map(({ component }) => {
                    const decompositionRequests = selectedRows.map((bom: EntityDetail) => ({
                        component: {
                            idType: 'id',
                            idValue: bom.id,
                        },
                        attributes: values[bom.id],
                    }));
                    return {
                        subParams: { decompositionType: bomType },
                        body: {
                            assembly: {
                                idType: 'id',
                                idValue: component.id,
                            },
                            decompositions: decompositionRequests,
                        },
                    };
                });

                const data = batchRequestBody(
                    entityUrls.createDecomposition.method,
                    entityUrls.createDecomposition.url,
                    flatten(bodyData)
                );
                const res = await batchRequest({
                    ...entityUrls.batchRequest,
                    data,
                });

                handleBatchRequestRes({
                    response: res,
                    onSuccess: () => {
                        onClose();
                        onCloseDrawer();
                    },
                    successMessage: commonMessages.successfullyAddedBOMs,
                });
            } catch (error) {
                notifyError(<span>{commonMessages.failedToAddingBOMs}</span>);
            } finally {
                refreshTable && refreshTable();
                gridRef.current.api.hideOverlay();
                setLoading(false);
            }
        }
    };
    return (
        <Box
            sx={{
                display: 'flex',
                height: '100%',
                width: '100%',
                flexDirection: 'column',
                alignItems: 'center',
                ...tableStyles,
            }}
        >
            <Box
                className="ag-theme-alpine"
                sx={{
                    height: '100%',
                    width: '100%',
                }}
            >
                <Formik
                    innerRef={formikRef}
                    enableReinitialize
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={(values, { resetForm, setSubmitting }) => {
                        handleSubmit(values, setSubmitting);
                    }}
                >
                    {({ values, setFieldValue, ...rest }) => {
                        return (
                            // @ts-ignore
                            <AgGridReact
                                ref={gridRef}
                                {...gridOptions}
                                rowData={data}
                                context={{ schemaTreeMap: schemaTreeMap }}
                            />
                        );
                    }}
                </Formik>
            </Box>
            <Box
                sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginTop: '16px',
                }}
            >
                <Button
                    sx={{
                        width: '160px',
                        justifyContent: 'flex-start',
                        mr: '8px',
                        maxWidth: '160px',
                    }}
                    variant="contained"
                    color="secondary"
                    onClick={onClose}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    sx={{
                        width: '100%',
                        justifyContent: 'flex-start',
                        maxWidth: '260px',
                    }}
                    variant="contained"
                    color="info"
                    onClick={formikRef.current?.handleSubmit}
                    disabled={loading}
                >
                    Confirm
                </Button>
            </Box>
        </Box>
    );
};
export default BOMCart;
