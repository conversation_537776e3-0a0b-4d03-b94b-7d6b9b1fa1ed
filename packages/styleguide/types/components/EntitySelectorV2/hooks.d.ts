import { UseEntitySearchOptions, UseEntitySearchResult, LoadOptionsResult } from './types';
/**
 * Custom hook for entity search functionality
 * Handles debounced search, loading states, and abort controllers
 */
export declare function useEntitySearch({ entityType, searchText, selectedTab, }: UseEntitySearchOptions): UseEntitySearchResult;
/**
 * Custom hook for async select load options functionality
 */
export declare function useAsyncLoadOptions(entityType: string): (search: string, loadedOptions?: any[]) => Promise<LoadOptionsResult>;
//# sourceMappingURL=hooks.d.ts.map