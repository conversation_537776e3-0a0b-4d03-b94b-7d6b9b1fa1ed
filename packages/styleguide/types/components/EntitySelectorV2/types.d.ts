import { PopperProps } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
import { EntityDetail } from '../../utils/checkingPermission';
export interface EntitySelectorV2Props {
    label: string;
    value: EntityDetail;
    entityType: string;
    isMulti?: boolean;
    onChange: (value: EntityDetail) => void;
    error?: boolean;
    helperText?: string;
    onBlur?: () => void;
}
export interface PopperComponentProps extends PopperProps {
    setOptions: Dispatch<SetStateAction<EntityDetail[]>>;
    entityType: string;
}
export interface AssigneeAvatarProps {
    name: string;
    email?: string;
}
export interface AgentOptionProps extends React.HTMLAttributes<HTMLLIElement> {
    key: any;
}
export interface SearchInputProps {
    searchText: string;
    onSearchTextChange: (text: string) => void;
    loading?: boolean;
}
export interface TabSelectorProps {
    entityType: string;
    selectedTab?: string;
    onTabChange: (event: any, value: string) => void;
}
export interface OptionsListProps {
    loading: boolean;
    children: React.ReactNode;
}
export interface UseEntitySearchOptions {
    entityType: string;
    searchText: string;
    selectedTab?: string;
}
export interface UseEntitySearchResult {
    options: EntityDetail[];
    loading: boolean;
    setOptions: Dispatch<SetStateAction<EntityDetail[]>>;
}
export interface LoadOptionsParams {
    search: string;
    loadedOptions?: any[];
}
export interface LoadOptionsResult {
    options: any[];
    hasMore: boolean;
}
//# sourceMappingURL=types.d.ts.map