/**
 * Generates a color based on a string input using HSL color space
 * @param string - The input string to generate color from
 * @returns HSL color string
 */
export declare function stringToColor(string: string): string;
/**
 * Builds search query for entity search based on entity type
 * @param search - The search text
 * @param entityType - The type of entity being searched
 * @returns Array of search query objects
 */
export declare function buildSearchQuery(search: string, entityType: string): Record<string, any>[];
/**
 * Extracts initials from a name string
 * @param name - Full name string
 * @returns Initials string (max 2 characters)
 */
export declare function getInitials(name: string): string;
//# sourceMappingURL=utils.d.ts.map