/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    buildContainsQuery,
    buildOrOperatorQuery,
    SYSTEM_ENTITY_TYPE,
    fetch,
    getAvatarUrl,
    entityUrls,
} from '@glidesystems/api';
import {
    Autocomplete,
    Avatar,
    Box,
    Chip,
    CircularProgress,
    ClickAwayListener,
    InputAdornment,
    Popper,
    PopperProps,
    styled,
    Tab,
    TabProps,
    Tabs,
    TextField,
    ThemeProvider,
    Typography,
} from '@mui/material';
import { Dispatch, ReactNode, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSchemaTree } from '@glidesystems/caching-store';
import debounce from 'lodash/debounce';
import { SearchIcon } from './icons/Icons';
import { AsyncSelect, MainTooltip } from '../glidesystems-styleguide';
import themes from '../themes';
import { nameToBackgroundColor } from '../utils/avatar';

const StyledPopper = styled(Popper)(({ theme }) => ({
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.12)',
    background: '#FFFFFF',
    '& .MuiPaper-root': {
        borderRadius: 0,
        backgroundColor: theme.palette.glide.background.normal.white,
        boxShadow: 'none',
    },
    '& .MuiAutocomplete-listbox': {
        backgroundColor: theme.palette.glide.background.normal.white,
    },
}));

const TabItem = styled(Tab)<TabProps>(({ theme }) => ({
    fontSize: '14px',
    minHeight: '19px',
    marginRight: '2px',
    padding: '12px 40px 8px 12px',
    textTransform: 'none',
    color: theme.palette.glide.text.normal.inversePrimary,
    '&.Mui-selected': {
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 700,
    },
    '&.Mui-disabled': {
        color: '#C2C2C2',
        borderColor: '#C2C2C2',
    },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
    marginTop: '8px',
    minHeight: '28px',
    marginBottom: '-2px',
    '& .MuiTabs-indicator': { backgroundColor: theme.palette.glide.stroke.normal.main },
}));

const AGENT_TYPE_ORDER = {
    Person: 1,
    Team: 2,
    Department: 3,
    InternalCompany: 4,
    ExternalCompany: 5,
};

const PopperComponent = ({
    setOptions,
    entityType,
    ...props
}: PopperProps & {
    setOptions: Dispatch<SetStateAction<EntitySelectOption[]>>;
    entityType: string;
}) => {
    const [selectedTab, setSelectedTab] = useState<string>();
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const abortRef = useRef<AbortController | null>(null);
    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);
    const subTypes = useMemo(
        () =>
            schemaTreeMap
                ? Object.values(schemaTreeMap)
                      .filter((schema) => schema.path.includes(entityType) && !schema.abstract)
                      .sort((a, b) => {
                          // for agent types, we want to sort them in a specific order
                          const rankA =
                              AGENT_TYPE_ORDER[a.name] !== undefined
                                  ? AGENT_TYPE_ORDER[a.name]
                                  : Number.MAX_SAFE_INTEGER;
                          const rankB =
                              AGENT_TYPE_ORDER[b.name] !== undefined
                                  ? AGENT_TYPE_ORDER[b.name]
                                  : Number.MAX_SAFE_INTEGER;
                          if (rankA !== rankB) return rankA - rankB;
                          return a.displayName.localeCompare(b.displayName);
                      })
                : [],

        [schemaTreeMap, entityType]
    );

    useEffect(() => {
        if (subTypes.length > 0) {
            setSelectedTab(subTypes[0].name);
        }
    }, [subTypes]);

    const onTabChanged = (e, value) => {
        setSearchText('');
        setSelectedTab(value);
    };

    const fetchData = useCallback(
        debounce(async (entityType, searchText) => {
            try {
                abortRef.current?.abort();
                abortRef.current = new AbortController();
                setLoading(true);
                let query;
                if (searchText) {
                    const searchQuery = [
                        buildContainsQuery('name', searchText),
                        buildContainsQuery('description', searchText),
                        buildContainsQuery('title', searchText),
                    ];
                    if (entityType === SYSTEM_ENTITY_TYPE.PERSON) {
                        searchQuery.push(buildContainsQuery('email', searchText));
                    }
                    query = JSON.stringify(buildOrOperatorQuery(searchQuery));
                }

                const { data } = await fetch({
                    ...entityUrls.getListEntity,
                    params: { entityType },
                    qs: {
                        limit: 100,
                        sort: 'name,ASCENDING',
                        query: query,
                    },
                    signal: abortRef.current.signal,
                });

                const options = data?.data?.map((option) => ({
                    value: option.id,
                    type: option?.properties?.type,
                    label: `${option.properties.name}${
                        option.properties.revision ? ' - ' + option.properties.revision : ''
                    }`,
                    email: option?.properties?.email,
                }));
                setOptions(options);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        }, 300),
        []
    );

    useEffect(() => {
        fetchData(selectedTab, searchText);
    }, [selectedTab, searchText]);

    return (
        <StyledPopper {...props}>
            <Box sx={{ padding: '8px' }}>
                <TextField
                    autoFocus
                    size="small"
                    sx={{
                        '& input': {
                            paddingLeft: '4px !important',
                            fontSize: '14px',
                            height: '24px',
                        },
                    }}
                    fullWidth
                    placeholder="Type to search"
                    value={searchText}
                    onChange={(e) => {
                        setSearchText((e.target as any).value);
                    }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
                <StyledTabs
                    variant="scrollable"
                    value={selectedTab}
                    scrollButtons="auto"
                    onChange={onTabChanged}
                    allowScrollButtonsMobile
                >
                    {subTypes.map((subType) => (
                        <TabItem key={subType.name} value={subType.name} label={subType.displayName} />
                    ))}
                </StyledTabs>
            </Box>
            {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '24px' }}>
                    <CircularProgress size={48} />
                </Box>
            ) : (
                <Box sx={{ maxHeight: '200px', overflow: 'auto' }}>{props.children as ReactNode}</Box>
            )}
        </StyledPopper>
    );
};

const UserAvatar = ({ name, email }: { name: string; email?: string }) => {
    return (
        <MainTooltip title={email}>
            <Avatar
                sizes="small"
                sx={{ bgcolor: nameToBackgroundColor(name), width: 26, height: 26, border: '1px solid white' }}
                children={`${name.split(' ')[0][0]}${name.split(' ')?.[1]?.[0] || ''}`}
                className="avatar"
                alt={name}
                src={email ? getAvatarUrl(email) : ''}
            />
        </MainTooltip>
    );
};
const EntityOption = (props: React.HTMLAttributes<HTMLLIElement> & { key: any }, option: EntitySelectOption) => {
    const { label, email, type, value } = option;
    return (
        <Box key={value} {...(props as any)}>
            <Typography
                sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    lineHeight: '16px',
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center',
                    padding: '4px 8px',
                }}
            >
                <UserAvatar name={label} email={email} />
                {label}
            </Typography>
        </Box>
    );
};

const buildSearchQuery = (search: string, entityType: string) => {
    const commonSearchFields = [buildContainsQuery('name', search), buildContainsQuery('description', search)];

    if (entityType === SYSTEM_ENTITY_TYPE.PERSON) {
        return [...commonSearchFields, buildContainsQuery('email', search)];
    }

    return commonSearchFields;
};

export interface EntitySelectOption {
    value: string;
    label: string;
    type: string;
    email?: string;
}

const InputComponent = ({
    label,
    avatarUrl,
    title,
    onDelete,
}: {
    label: string;
    avatarUrl?: string;
    title: string;
    onDelete?: () => void;
}) => {
    return (
        <MainTooltip title={title}>
            <Chip
                size="small"
                avatar={avatarUrl ? <Avatar alt={label} src={avatarUrl} /> : undefined}
                label={label}
                onDelete={onDelete}
                sx={{ marginRight: '4px' }}
            />
        </MainTooltip>
    );
};

const EntitySelectorV2 = ({
    label,
    value,
    entityType,
    isMulti,
    onChange,
    error,
    helperText,
    onBlur = () => {},
}: {
    label: string;
    value: EntitySelectOption;
    entityType: string;
    isMulti?: boolean;
    onChange: (value: EntitySelectOption | EntitySelectOption[]) => void;
    error?: boolean;
    helperText?: string;
    onBlur?: () => void;
}) => {
    const [open, setOpen] = useState(false);
    const [options, setOptions] = useState<EntitySelectOption[]>([]);
    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);
    const popperComponent = useMemo(
        () => (props: PopperProps) => <PopperComponent {...props} setOptions={setOptions} entityType={entityType} />,
        [entityType]
    );

    const loadOptions = async (search: string, loadedOptions: EntitySelectOption[]) => {
        try {
            const offset = loadedOptions?.length || 0;
            const query = buildOrOperatorQuery(buildSearchQuery(search, entityType));

            const { data } = await fetch({
                ...entityUrls.getListEntity,
                params: { entityType },
                qs: {
                    offset,
                    limit: 20,
                    sort: 'name,ASCENDING',
                    query: JSON.stringify(query),
                },
            });

            const options =
                data?.data?.map((option) => ({
                    value: option.id,
                    type: option?.properties?.type,
                    label: `${option.properties.name}${
                        option.properties.revision ? ' - ' + option.properties.revision : ''
                    }`,
                })) || [];

            return {
                options,
                hasMore: data.pageInfo.total > offset + options.length,
            };
        } catch {
            return {
                options: [],
                hasMore: false,
            };
        }
    };

    const isAbstract = schemaTreeMap?.[entityType]?.abstract;

    const safeValue = isMulti ? (Array.isArray(value) ? value : []) : value ?? undefined;

    if (!isAbstract) {
        return (
            <AsyncSelect
                isDisabled={!schemaTreeMap}
                label={label}
                isMulti={isMulti}
                closeMenuOnSelect={!isMulti}
                loadOptions={loadOptions}
                getOptionValue={(option) => option.value}
                onChange={onChange}
                value={safeValue}
                onBlur={onBlur}
            />
        );
    }

    return (
        <ThemeProvider theme={themes.default}>
            <ClickAwayListener
                onClickAway={() => {
                    setOpen(false);
                }}
            >
                <Box>
                    <Autocomplete
                        inputValue=""
                        options={options}
                        fullWidth
                        open={open}
                        onFocus={(e) => {
                            setOpen(true);
                        }}
                        onChange={(_, newValue) => onChange(newValue)}
                        value={safeValue}
                        multiple={isMulti}
                        getOptionLabel={(option) => {
                            return option.label;
                        }}
                        onBlur={onBlur}
                        isOptionEqualToValue={(option, value) => option.value === value.value}
                        disabled={!schemaTreeMap}
                        ListboxProps={{
                            sx: {
                                maxHeight: '200px',
                                // '& .MuiAutocomplete-option, .MuiAutocomplete-option[aria-selected="true"]': {
                                //     '&:hover': {
                                //         background: '#334466 !important',
                                //         color: '#fff !important',
                                //     },
                                // },
                            },
                        }}
                        renderOption={EntityOption}
                        disableCloseOnSelect
                        autoFocus={false}
                        PopperComponent={popperComponent}
                        renderInput={(params) => (
                            <TextField
                                autoComplete="off"
                                label={label}
                                placeholder={!schemaTreeMap ? 'Loading...' : 'Select...'}
                                error={error}
                                helperText={helperText}
                                {...params}
                                InputLabelProps={{ shrink: true }}
                                fullWidth
                                size="small"
                                InputProps={{
                                    ...params.InputProps,
                                    startAdornment: (
                                        <>
                                            {value && (
                                                <>
                                                    {isMulti && Array.isArray(value)
                                                        ? // Multi-select: render each selected value with map
                                                          value.map((selectedValue, index) => (
                                                              <InputComponent
                                                                  key={`${selectedValue.value}-${index}`}
                                                                  label={selectedValue.label || ''}
                                                                  avatarUrl={
                                                                      selectedValue.email
                                                                          ? getAvatarUrl(selectedValue.email)
                                                                          : undefined
                                                                  }
                                                                  title={selectedValue.email || selectedValue.type}
                                                                  onDelete={() => {
                                                                      const newValues = value.filter(
                                                                          (v) => v.value !== selectedValue.value
                                                                      );
                                                                      onChange(newValues);
                                                                  }}
                                                              />
                                                          ))
                                                        : !Array.isArray(value) && (
                                                              <InputComponent
                                                                  label={value.label || ''}
                                                                  avatarUrl={
                                                                      value.email
                                                                          ? getAvatarUrl(value.email)
                                                                          : undefined
                                                                  }
                                                                  title={value.email || value.type}
                                                              />
                                                          )}
                                                </>
                                            )}
                                        </>
                                    ),
                                }}
                                inputProps={{
                                    ...params.inputProps,
                                    readOnly: true,
                                    tabIndex: -1,
                                }}
                                sx={{
                                    '& input': { caretColor: 'transparent' },
                                }}
                            />
                        )}
                    />
                </Box>
            </ClickAwayListener>
        </ThemeProvider>
    );
};

export default EntitySelectorV2;
