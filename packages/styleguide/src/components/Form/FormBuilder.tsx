/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import * as yup from 'yup';
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { Grid, MenuItem, TextField as Text } from '@mui/material';
import { TextField, CheckboxWithLabel } from 'formik-mui';
import { Field } from 'formik';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format } from 'date-fns';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import RichTextEditor from '../Base/RichTextEditor';
import { AttributeType } from '@glidesystems/api';
import { useGlobalConfig } from '@glidesystems/caching-store';
const buildRequireMessage = (displayName) => {
    return `${displayName} is required`;
};

const buildMinMessage = (displayName, minValue) => {
    return `${displayName} must be greater than or equal to ${minValue}`;
};

const buildMaxMessage = (displayName, maxValue) => {
    return `${displayName} must be less than or equal to ${maxValue}`;
};

const folderPaths = [{ name: 'OwnerFolder', label: 'Owner Folder' }];

export const formatDate = (date) => {
    try {
        return format(date, 'yyyy-MM-dd');
    } catch (err) {
        return date;
    }
};

export const formatDateTime = (date) => {
    try {
        return format(date, "yyyy-MM-dd'T'HH:mm:ss");
    } catch (err) {
        return date;
    }
};
const getArrayValidator = (attributeType, displayName, nullable) => {
    let baseValidator;

    switch (attributeType) {
        case AttributeType.STRING_ARRAY:
            baseValidator = yup.array().of(yup.string().strict().required());
            break;
        case AttributeType.DATE_ARRAY:
        case AttributeType.DATE_TIME_ARRAY:
            baseValidator = yup.array().of(yup.date().typeError(`${displayName} must be a valid date`).required());
            break;
        case AttributeType.FLOAT_ARRAY:
            baseValidator = yup.array().of(yup.number().typeError(`${displayName} must be a number`).required());
            break;
        case AttributeType.INTEGER_ARRAY:
            baseValidator = yup
                .array()
                .of(yup.number().integer().typeError(`${displayName} must be an integer`).required());
            break;
        default:
            return null;
    }

    let yupValidator = baseValidator.nullable().default([]);

    if (!isNil(nullable) && !Boolean(nullable)) {
        yupValidator = yupValidator.test(
            'is-required',
            buildRequireMessage(displayName),
            (value) => Array.isArray(value) && value.length > 0
        );
    }

    return yupValidator;
};

const groupOptions = (agents, group) => {
    return agents.map((agent) => ({
        label: agent.properties.name,
        id: agent.id,
        group,
    }));
};

export const buildOwnerSelection = (companies, teams, departments) => {
    return [
        ...groupOptions(companies, 'Company'),
        ...groupOptions(teams, 'Team'),
        ...groupOptions(departments, 'Department'),
    ];
};

export const buildValidationSchema = (entityAttributeList) =>{
    console.log("entityAttributeList", entityAttributeList);
    return yup.object().shape(
        entityAttributeList.reduce((prev, cur) => {
            const { name, type, nullable, hyperlink, constraint, displayName, unitOfMeasure } = cur;
            const enumRange = get(constraint, 'enumRange', []);
            const pattern = get(constraint, 'pattern', null);
            const minLength = get(constraint, 'minLength', null);
            const maxLength = get(constraint, 'maxLength', null);
            switch (type) {
                case AttributeType.LONG:
                case AttributeType.FLOAT:
                case AttributeType.INTEGER: {
                    let yupValidator = yup.number().typeError(`${displayName} must be a number`);

                    if (minLength) {
                        yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
                    }
                    if (maxLength) {
                        yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
                    }

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    if (!isEmpty(unitOfMeasure)) {
                        return {
                            ...prev,
                            [name]: yupValidator,
                            [`${name}:unit`]: yup.string(),
                        };
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.STRING:
                case AttributeType.TEXT: {
                    let yupValidator = yup.string();

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                        yupValidator = yupValidator.typeError(buildRequireMessage(displayName));

                        if (enumRange.length > 0) {
                            yupValidator = yupValidator.oneOf(enumRange);

                            return {
                                ...prev,
                                [name]: yupValidator,
                            };
                        }
                    }
                    if (minLength) {
                        yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
                    }
                    if (maxLength) {
                        yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
                    }

                    if (pattern) {
                        yupValidator = yupValidator.matches(
                            new RegExp(pattern, 'g'),
                            `Value does not match ${pattern}`
                        );
                    }

                    if (hyperlink) {
                        const regMatch =
                            /^((http|https):\/\/)?(www.)?(?!.*(http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+(\/)?.([\w\?[a-zA-Z-_%\/@?]+)*([^\/\w\?[a-zA-Z0-9_-]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?$/;
                        yupValidator = yupValidator.matches(regMatch, `Value should be a valid url`);
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.BOOLEAN: {
                    let yupValidator = yup.boolean();

                    if (isNil(nullable) || Boolean(nullable)) {
                        yupValidator = yupValidator.nullable();
                    } else {
                        yupValidator = yupValidator
                            .required(buildRequireMessage(displayName))
                            .typeError(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }

                case AttributeType.DATE: {
                    let yupValidator = yup.string().nullable();

                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }
                case AttributeType.DATE_TIME: {
                    let yupValidator = yup.string().nullable();

                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                        yupValidator = yupValidator.typeError(buildRequireMessage(displayName));
                    }

                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }
                case AttributeType.ASYNC_SELECT: {
                    let yupValidator = cur.isMulti ? yup.array().nullable() : yup.object().nullable();
                    if (!isNil(nullable) && !Boolean(nullable)) {
                        yupValidator = yupValidator.required(buildRequireMessage(displayName));
                        yupValidator = yupValidator.typeError(buildRequireMessage(displayName));
                    }
                    return {
                        ...prev,
                        [name]: yupValidator,
                    };
                }
                case AttributeType.STRING_ARRAY:
                case AttributeType.DATE_ARRAY:
                case AttributeType.DATE_TIME_ARRAY:
                case AttributeType.FLOAT_ARRAY:
                case AttributeType.INTEGER_ARRAY:
                    return {
                        ...prev,
                        [name]: getArrayValidator(type, displayName, nullable),
                    };
                default:
                    return prev;
            }
        }, {})
   )};

export const buildFormItem = (attribute, setFieldValue, size = 'medium') => {
    /**
     * url, method, datasource & multiSelect are properties supports for relation
     */
    const { id, displayName, name, description, nullable, url, method, datasource, multiSelect } = attribute;
    const isRequired = isNil(nullable) ? false : !Boolean(nullable);
    if (attribute.richText) {
        return (
            <Grid item xs={12} key={id}>
                <Field
                    fullWidth
                    variant="outlined"
                    id={id}
                    label={displayName}
                    name={name}
                    helperText={description}
                    rows={4}
                    disabled={false}
                    required={isRequired}
                    component={RichTextEditor}
                    size={size}
                />
            </Grid>
        );
    }

    switch (attribute.type) {
        case AttributeType.INTEGER:
        case AttributeType.LONG:
        case AttributeType.FLOAT:
            return (
                <Grid item xs={12} key={id}>
                    <Field
                        fullWidth
                        variant="outlined"
                        component={TextField}
                        label={displayName}
                        required={isRequired}
                        name={attribute.name}
                        disabled={false}
                        helperText={attribute.description}
                        InputLabelProps={{ shrink: true }}
                        size={size}
                        onChange={(e) => {
                            setFieldValue(name, e.target.value);
                        }}
                    />
                </Grid>
            );

        case AttributeType.BOOLEAN:
            return (
                <Grid item key={id} xs={12}>
                    <Field
                        fullWidth
                        component={CheckboxWithLabel}
                        type="checkbox"
                        name={name}
                        size={size}
                        required={isRequired}
                        onChange={(e) => {
                            setFieldValue(name, e.target.checked);
                        }}
                        Label={{
                            label: displayName,
                        }}
                        helperText={description}
                    />
                </Grid>
            );

        case AttributeType.STRING: {
            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (enumRange.length > 0) {
                return (
                    <Grid item xs={12} key={id}>
                        <Field
                            size={size}
                            fullWidth
                            select
                            type="text"
                            variant="outlined"
                            name={name}
                            label={displayName}
                            required={isRequired}
                            helperText={description}
                            component={TextField}
                            InputLabelProps={{ shrink: true }}
                            disabled={false}
                            onChange={(e) => {
                                setFieldValue(name, e.target.value);
                            }}
                        >
                            {enumRange.map((enumValue) => (
                                <MenuItem value={enumValue} key={enumValue}>
                                    {enumValue}
                                </MenuItem>
                            ))}
                        </Field>
                    </Grid>
                );
            }

            if (attribute.name === 'fileLocation') {
                return (
                    <Grid item key={id} sx={{ width: '100%' }}>
                        <Grid item xs={12}>
                            <Field
                                size={size}
                                fullWidth
                                select
                                type="text"
                                variant="outlined"
                                name="filePath"
                                label="Folder Path"
                                required={nullable}
                                component={TextField}
                                InputLabelProps={{ shrink: true }}
                                helperText={description}
                            >
                                {folderPaths.map((folder) => (
                                    <MenuItem value={folder.name} key={folder.name}>
                                        {folder.label}
                                    </MenuItem>
                                ))}
                            </Field>
                        </Grid>
                    </Grid>
                );
            } else {
                return (
                    <Grid item xs={12} key={id}>
                        <Field
                            size={size}
                            fullWidth
                            variant="outlined"
                            component={TextField}
                            required={isRequired}
                            label={displayName}
                            name={name}
                            helperText={description}
                            disabled={false}
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                );
            }
        }
        case AttributeType.TEXT: {
            return (
                <Grid item xs={12} key={id}>
                    <Field
                        size={size}
                        fullWidth
                        multiline
                        variant="outlined"
                        component={TextField}
                        required={isRequired}
                        label={displayName}
                        name={name}
                        helperText={description}
                        disabled={false}
                        InputLabelProps={{ shrink: true }}
                        sx={{
                            textarea: {
                                resize: 'vertical',
                                width: '100%',
                                padding: '0px !important',
                                minHeight: '50px',
                            },
                        }}
                    />
                </Grid>
            );
        }
        case AttributeType.DATE:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DatePicker
                                        label={displayName}
                                        value={field.value || ''}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatDate(newValue), true);
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateFormat()}
                                        disabled={false}
                                        renderInput={(params) => (
                                            <Text
                                                //@ts-ignore
                                                size={size}
                                                {...params}
                                                error={isErr}
                                                InputLabelProps={{ shrink: true }}
                                                required={isRequired}
                                                helperText={isErr ? error : description}
                                                fullWidth
                                                onBlur={() => form.setFieldTouched(name)}
                                            />
                                        )}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.DATE_TIME:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DateTimePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatDateTime(newValue));
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                                        renderInput={(params) => {
                                            return (
                                                <Text
                                                    //@ts-ignore
                                                    size={size}
                                                    InputLabelProps={{ shrink: true }}
                                                    {...params}
                                                    error={isErr}
                                                    required={isRequired}
                                                    helperText={isErr ? error : description}
                                                    fullWidth
                                                    onBlur={() => form.setFieldTouched(name)}
                                                />
                                            );
                                        }}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );

        default:
            return null;
    }
};

export const buildInitialValue = (entityAttributeList, defaultValues) =>
    entityAttributeList.reduce((prev, cur) => {
        if (cur.type === AttributeType.DATE) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatDate(Date.parse(cur.defaultValue))
                : null;
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }
        if (cur.type === AttributeType.DATE_TIME) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatDateTime(Date.parse(cur.defaultValue))
                : null;
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }

        if ([AttributeType.LONG, AttributeType.FLOAT, AttributeType.INTEGER].includes(cur.type) && cur.unitOfMeasure) {
            return {
                ...prev,
                [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
                [`${cur.name}:unit`]: get(cur, 'unitOfMeasure.quantityUnit', ''),
            };
        }

        return {
            ...prev,
            [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
            filePath: folderPaths[0].name,
        };
    }, {});
